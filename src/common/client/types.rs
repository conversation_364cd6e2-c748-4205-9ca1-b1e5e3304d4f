use std::time::Duration;

use validator::<PERSON>ida<PERSON>;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct ShredstreamClientConfig {
    pub connect_timeout: Option<Duration>,
    pub subscribe_timeout: Option<Duration>,
}

impl Default for ShredstreamClientConfig {
    fn default() -> Self {
        Self { connect_timeout: Some(Duration::from_secs(10)), subscribe_timeout: Some(Duration::from_secs(5)) }
    }
}

#[derive(Debug, <PERSON><PERSON>, Validate)]
pub struct ShredstreamSubscriptionFilters {
    pub accounts: Option<Vec<String>>,
}

impl Default for ShredstreamSubscriptionFilters {
    fn default() -> Self {
        Self { accounts: None }
    }
}
