use std::collections::HashMap;

use super::types::ShredstreamSubscriptionFilters;
use crate::generated::{SubscribeEntriesRequest, SubscribeRequestFilterAccounts};

pub fn create_subscription_request(filters: Option<ShredstreamSubscriptionFilters>) -> SubscribeEntriesRequest {
    let mut accounts = HashMap::new();

    let transactions = HashMap::new();
    let slots = HashMap::new();

    if let Some(filters) = filters {
        if let Some(account_list) = filters.accounts {
            if !account_list.is_empty() {
                accounts.insert("".to_owned(), SubscribeRequestFilterAccounts {
                    account: account_list,
                    owner: vec![],
                    filters: vec![],
                    nonempty_txn_signature: None,
                });
            }
        }
    }

    SubscribeEntriesRequest { accounts, transactions, slots, commitment: None }
}
