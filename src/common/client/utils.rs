use std::collections::HashMap;

use super::types::ShredstreamSubscriptionFilters;
use crate::generated::{
    CommitmentLevel, SubscribeEntriesRequest, SubscribeRequestFilterAccounts, SubscribeRequestFilterSlots,
    SubscribeRequestFilterTransactions,
};

pub fn create_subscription_request(filters: Option<ShredstreamSubscriptionFilters>) -> SubscribeEntriesRequest {
    let mut accounts = HashMap::new();
    let mut transactions = HashMap::new();
    let mut slots = HashMap::new();

    if let Some(filters) = filters {
        if let Some(account_list) = filters.accounts {
            if !account_list.is_empty() {
                accounts.insert("".to_owned(), SubscribeRequestFilterAccounts {
                    account: account_list,
                    owner: vec![],
                    filters: vec![],
                    nonempty_txn_signature: None,
                });

                transactions.insert("".to_owned(), SubscribeRequestFilterTransactions {
                    account_include: vec!["".to_owned()],
                    account_exclude: vec!["".to_owned()],
                    account_required: vec!["".to_owned()],
                });

                slots.insert("".to_owned(), SubscribeRequestFilterSlots {
                    filter_by_commitment: Some(true),
                    interslot_updates: Some(false),
                });
            }
        }
    }

    SubscribeEntriesRequest { accounts, transactions, slots, commitment: Some(CommitmentLevel::Processed as i32) }
}
