use std::{fmt, time::Duration};

#[derive(Debug)]
pub enum ShredstreamError {
    InvalidEndpoint(String),
    ConnectionFailed(String),
    TlsConfigurationFailed(String),
    SubscriptionFailed(String),
    SubscriptionTimeout(Duration),
    TransportError(String),
    InvalidConfiguration(String),
}

impl fmt::Display for ShredstreamError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ShredstreamError::InvalidEndpoint(endpoint) => {
                write!(f, "Invalid endpoint URL: {}", endpoint)
            }
            ShredstreamError::ConnectionFailed(reason) => {
                write!(f, "Failed to establish connection: {}", reason)
            }
            ShredstreamError::TlsConfigurationFailed(reason) => {
                write!(f, "Failed to configure TLS: {}", reason)
            }
            ShredstreamError::SubscriptionFailed(reason) => {
                write!(f, "Failed to subscribe to entries: {}", reason)
            }
            ShredstreamError::SubscriptionTimeout(duration) => {
                write!(f, "Subscribe operation timed out after {:?}", duration)
            }
            ShredstreamError::TransportError(reason) => {
                write!(f, "Transport error: {}", reason)
            }
            ShredstreamError::InvalidConfiguration(reason) => {
                write!(f, "Invalid configuration: {}", reason)
            }
        }
    }
}

impl std::error::Error for ShredstreamError {}

impl From<tonic::transport::Error> for ShredstreamError {
    fn from(error: tonic::transport::Error) -> Self {
        ShredstreamError::TransportError(error.to_string())
    }
}

impl From<tonic::Status> for ShredstreamError {
    fn from(status: tonic::Status) -> Self {
        ShredstreamError::SubscriptionFailed(status.to_string())
    }
}

pub type Result<T> = std::result::Result<T, ShredstreamError>;
