use tonic::{codec::Streaming, transport::Channel};

use super::{
    errors::{Result, ShredstreamError},
    types::{ShredstreamClientConfig, ShredstreamSubscriptionFilters},
    utils::create_subscription_request,
};
use crate::generated::{Entry, shredstream_proxy_client::ShredstreamProxyClient};

pub struct ShredstreamClient {
    endpoint: String,
    filters: Option<ShredstreamSubscriptionFilters>,
    config: ShredstreamClientConfig,
    client: Option<ShredstreamProxyClient<Channel>>,
}

impl ShredstreamClient {
    pub fn new(
        endpoint: String,
        filters: Option<ShredstreamSubscriptionFilters>,
        config: Option<ShredstreamClientConfig>,
    ) -> Self {
        Self { endpoint, filters, config: config.unwrap_or_default(), client: None }
    }

    pub async fn subscribe(&mut self) -> Result<Streaming<Entry>> {
        self.ensure_connected().await?;

        let request = create_subscription_request(self.filters.clone());
        let result = self.subscribe_with_timeout(request).await;

        match result {
            Ok(response) => Ok(response.into_inner()),
            Err(e) => {
                self.reset();
                Err(e)
            }
        }
    }

    async fn connect(&mut self) -> Result<()> {
        let mut endpoint = self.create_endpoint()?;
        self.configure_timeout(&mut endpoint);
        self.configure_tls(&mut endpoint)?;

        let channel = endpoint.connect().await.map_err(|e| ShredstreamError::ConnectionFailed(e.to_string()))?;

        self.client = Some(ShredstreamProxyClient::new(channel));
        Ok(())
    }

    fn create_endpoint(&self) -> Result<tonic::transport::Endpoint> {
        tonic::transport::Endpoint::from_shared(self.endpoint.clone())
            .map_err(|_| ShredstreamError::InvalidEndpoint(self.endpoint.clone()))
    }

    fn configure_timeout(&self, endpoint: &mut tonic::transport::Endpoint) {
        if let Some(timeout) = self.config.connect_timeout {
            *endpoint = endpoint.clone().connect_timeout(timeout);
        }
    }

    fn configure_tls(&self, endpoint: &mut tonic::transport::Endpoint) -> Result<()> {
        if self.endpoint.starts_with("https://") {
            let tls_config = tonic::transport::ClientTlsConfig::new().with_webpki_roots().assume_http2(true);

            *endpoint = endpoint
                .clone()
                .tls_config(tls_config)
                .map_err(|e| ShredstreamError::TlsConfigurationFailed(e.to_string()))?;
        }
        Ok(())
    }

    async fn ensure_connected(&mut self) -> Result<()> {
        if self.client.is_none() {
            self.connect().await?;
        }
        Ok(())
    }

    async fn subscribe_with_timeout(
        &mut self,
        request: crate::generated::SubscribeEntriesRequest,
    ) -> Result<tonic::Response<tonic::codec::Streaming<Entry>>> {
        use tokio::time::timeout;

        let subscribe_future = self.client.as_mut().unwrap().subscribe_entries(request);

        if let Some(timeout_duration) = self.config.subscribe_timeout {
            match timeout(timeout_duration, subscribe_future).await {
                Ok(response) => response.map_err(ShredstreamError::from),
                Err(_) => Err(ShredstreamError::SubscriptionTimeout(timeout_duration)),
            }
        } else {
            subscribe_future.await.map_err(ShredstreamError::from)
        }
    }

    fn reset(&mut self) {
        self.client = None;
    }
}
