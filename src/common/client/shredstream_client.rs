use anyhow::{Context, Result};
use tokio::time::timeout;
use tonic::{codec::Streaming, transport::Channel};

use super::{
    types::{ShredstreamClientConfig, ShredstreamSubscriptionFilters},
    utils::create_subscription_request,
};
use crate::generated::{Entry, shredstream_proxy_client::ShredstreamProxyClient};

pub struct ShredstreamClient {
    endpoint: String,
    filters: Option<ShredstreamSubscriptionFilters>,
    config: ShredstreamClientConfig,
    client: Option<ShredstreamProxyClient<Channel>>,
}

impl ShredstreamClient {
    pub fn new(
        endpoint: String,
        filters: Option<ShredstreamSubscriptionFilters>,
        config: Option<ShredstreamClientConfig>,
    ) -> Self {
        Self { endpoint, filters, config: config.unwrap_or_default(), client: None }
    }

    pub async fn subscribe(&mut self) -> Result<Streaming<Entry>> {
        if self.client.is_none() {
            self.connect().await.context("Failed to connect to shredstream endpoint")?;
        }

        let request = create_subscription_request(self.filters.clone());

        let subscribe_future = self.client.as_mut().unwrap().subscribe_entries(request);

        let result = if let Some(timeout_duration) = self.config.subscribe_timeout {
            match timeout(timeout_duration, subscribe_future).await {
                Ok(response) => response,
                Err(_) => {
                    self.reset();
                    return Err(anyhow::anyhow!("Subscribe operation timed out after {:?}", timeout_duration));
                }
            }
        } else {
            subscribe_future.await
        };

        match result {
            Ok(response) => Ok(response.into_inner()),
            Err(e) => {
                self.reset();
                Err(anyhow::anyhow!("Failed to subscribe to entries: {}", e))
            }
        }
    }

    async fn connect(&mut self) -> Result<()> {
        let mut endpoint =
            tonic::transport::Endpoint::from_shared(self.endpoint.clone()).context("Invalid endpoint URL")?;

        if let Some(timeout) = self.config.connect_timeout {
            endpoint = endpoint.connect_timeout(timeout);
        }

        if self.endpoint.starts_with("https://") {
            let tls_config = tonic::transport::ClientTlsConfig::new().with_webpki_roots().assume_http2(true);
            endpoint = endpoint.tls_config(tls_config).context("Failed to configure TLS")?;
        }

        let channel = endpoint.connect().await.context("Failed to establish connection")?;
        self.client = Some(ShredstreamProxyClient::new(channel));
        Ok(())
    }

    fn reset(&mut self) {
        self.client = None;
    }
}
