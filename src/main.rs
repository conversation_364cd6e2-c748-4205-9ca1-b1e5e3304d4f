mod common;
mod config;
mod core;
mod generated;
mod utils;

use core::{config::load_config, logger::Logger};
use std::time::Duration;

use common::client::{ShredstreamClient, ShredstreamClientConfig, ShredstreamSubscriptionFilters};
use futures_util::StreamExt;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    rustls::crypto::ring::default_provider().install_default().expect("Failed to install crypto provider");

    let config = load_config()?;
    let _logger = Logger::init(&config.logger)?;

    let endpoint = "https://shreds-far-point-1.erpc.global".to_string();

    let filters = Some(ShredstreamSubscriptionFilters {
        accounts: Some(vec!["6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P".to_string()]),
    });

    let config = Some(ShredstreamClientConfig {
        connect_timeout: Some(Duration::from_secs(10)),
        subscribe_timeout: Some(Duration::from_secs(5)),
    });

    let mut client = ShredstreamClient::new(endpoint, filters, config);

    println!("Connecting to Jito Shredstream...");
    let mut stream = client.subscribe().await?;
    println!("Connected! Listening for entries...");

    let mut entry_count = 0;
    while let Some(entry) = stream.next().await {
        match entry {
            Ok(entry) => {
                entry_count += 1;
                println!(
                    "Received entry #{}: slot={}, data_size={} bytes",
                    entry_count,
                    entry.slot,
                    entry.entries.len()
                );

                if entry_count >= 5 {
                    println!("Received {} entries successfully. Test completed!", entry_count);
                    break;
                }
            }
            Err(e) => {
                println!("Error receiving entry: {}", e);
                break;
            }
        }
    }

    Ok(())
}
